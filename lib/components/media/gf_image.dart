import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../core/gf_loading.dart';

/// GameFlex image component
///
/// A reusable image component that handles different image states and provides
/// consistent loading, error, and placeholder handling across the app.
///
/// Example usage:
/// ```dart
/// GFImage(
///   imageUrl: post.imageUrl,
///   aspectRatio: 16/9,
///   fit: BoxFit.cover,
///   onTap: () => _viewFullImage(),
/// )
/// ```
class GFImage extends StatelessWidget {
  final String? imageUrl;
  final double? width;
  final double? height;
  final double? aspectRatio;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool showLoadingIndicator;
  final Color? backgroundColor;

  const GFImage({
    super.key,
    this.imageUrl,
    this.width,
    this.height,
    this.aspectRatio,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.placeholder,
    this.errorWidget,
    this.onTap,
    this.onLongPress,
    this.showLoadingIndicator = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = _buildImageContent();

    if (aspectRatio != null) {
      imageWidget = AspectRatio(aspectRatio: aspectRatio!, child: imageWidget);
    }

    if (width != null || height != null) {
      imageWidget = SizedBox(width: width, height: height, child: imageWidget);
    }

    if (onTap != null || onLongPress != null) {
      imageWidget = GestureDetector(
        onTap: onTap,
        onLongPress: onLongPress,
        child: imageWidget,
      );
    }

    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: imageWidget,
    );
  }

  Widget _buildImageContent() {
    if (imageUrl == null || imageUrl!.isEmpty) {
      return _buildPlaceholder();
    }

    return Image.network(
      imageUrl!,
      fit: fit,
      loadingBuilder:
          showLoadingIndicator
              ? (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return _buildLoadingState();
              }
              : null,
      errorBuilder: (context, error, stackTrace) => _buildErrorState(),
    );
  }

  Widget _buildPlaceholder() {
    if (placeholder != null) return placeholder!;

    return Container(
      color: backgroundColor ?? AppColors.gfDarkBackground,
      child: const Center(
        child: Icon(
          Icons.image_outlined,
          color: AppColors.gfGrayText,
          size: 48,
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      color: backgroundColor ?? AppColors.gfDarkBackground,
      child: const Center(child: GFLoadingIndicator()),
    );
  }

  Widget _buildErrorState() {
    if (errorWidget != null) return errorWidget!;

    return Container(
      color: backgroundColor ?? AppColors.gfDarkBackground,
      child: const Center(
        child: Icon(
          Icons.broken_image_outlined,
          color: AppColors.gfGrayText,
          size: 48,
        ),
      ),
    );
  }
}

/// Lazy loading image component
class GFLazyImage extends StatefulWidget {
  final String? imageUrl;
  final double? width;
  final double? height;
  final double? aspectRatio;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const GFLazyImage({
    super.key,
    this.imageUrl,
    this.width,
    this.height,
    this.aspectRatio,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.placeholder,
    this.errorWidget,
    this.onTap,
    this.onLongPress,
  });

  @override
  State<GFLazyImage> createState() => _GFLazyImageState();
}

class _GFLazyImageState extends State<GFLazyImage> {
  @override
  Widget build(BuildContext context) {
    // For now, just use the regular GFImage component
    // In the future, we can add proper lazy loading with visibility detection
    return GFImage(
      imageUrl: widget.imageUrl,
      width: widget.width,
      height: widget.height,
      aspectRatio: widget.aspectRatio,
      fit: widget.fit,
      borderRadius: widget.borderRadius,
      placeholder: widget.placeholder,
      errorWidget: widget.errorWidget,
      onTap: widget.onTap,
      onLongPress: widget.onLongPress,
    );
  }
}

/// Circular image component (for avatars, profile pictures)
class GFCircularImage extends StatelessWidget {
  final String? imageUrl;
  final double radius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final Border? border;

  const GFCircularImage({
    super.key,
    this.imageUrl,
    this.radius = 24,
    this.placeholder,
    this.errorWidget,
    this.onTap,
    this.backgroundColor,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    Widget image =
        imageUrl != null && imageUrl!.isNotEmpty
            ? CircleAvatar(
              radius: radius,
              backgroundColor: backgroundColor ?? AppColors.gfDarkBackground,
              backgroundImage: NetworkImage(imageUrl!),
              onBackgroundImageError: (_, __) {},
            )
            : CircleAvatar(
              radius: radius,
              backgroundColor: backgroundColor ?? AppColors.gfDarkBackground,
              // Always provide fallback content in case the image fails to load
              child:
                  placeholder ??
                  Icon(Icons.person, size: radius, color: AppColors.gfGrayText),
            );

    if (border != null) {
      image = Container(
        decoration: BoxDecoration(shape: BoxShape.circle, border: border),
        child: image,
      );
    }

    if (onTap != null) {
      image = GestureDetector(onTap: onTap, child: image);
    }

    return image;
  }
}
