import 'package:flutter/material.dart';
import '../components/index.dart';
import '../models/post_model.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../screens/user_profile_screen.dart';

class FeedItem extends StatefulWidget {
  final PostModel post;
  final bool isVisible;

  const FeedItem({super.key, required this.post, this.isVisible = true});

  @override
  State<FeedItem> createState() => _FeedItemState();
}

class _FeedItemState extends State<FeedItem> {
  // Cache the video player widget to prevent rebuilds
  Widget? _cachedVideoPlayer;
  String? _cachedPostId;
  String? _cachedVideoUrl;

  @override
  void didUpdateWidget(FeedItem oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Only clear cache if the post ID actually changed (different post)
    if (oldWidget.post.id != widget.post.id) {
      AppLogger.debug('FeedItem: Post ID changed, clearing video cache');
      _cachedVideoPlayer = null;
      _cachedPostId = null;
      _cachedVideoUrl = null;
    } else {
      AppLogger.debug(
        'FeedItem: Post metadata updated, keeping existing video cache',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    AppLogger.debug(
      'FeedItem: Building widget for post ${widget.post.id} (cached video: ${_cachedVideoPlayer != null})',
    );
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          // Media content (image or video) - positioned to fill the entire area
          IgnorePointer(child: Positioned.fill(child: _buildMediaContent())),

          // Top overlay - User info and post content - positioned above media
          GFFeedTopOverlay(
            key: ValueKey('top_overlay_${widget.post.id}'),
            post: widget.post,
            onUserTap: () => _navigateToUserProfile(context),
          ),

          // Bottom overlay - Action buttons - positioned above media
          GFFeedBottomOverlay(
            key: ValueKey('bottom_overlay_${widget.post.id}'),
            post: widget.post,
          ),
        ],
      ),
    );
  }

  void _navigateToUserProfile(BuildContext context) {
    AppLogger.debug(
      'FeedItem: Avatar/User tapped! Navigating to user profile for user ${widget.post.userId}',
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => UserProfileScreen(
              userId: widget.post.userId,
              username: widget.post.username,
            ),
      ),
    );
  }

  Widget _buildMediaContent() {
    AppLogger.debug(
      'FeedItem: Building media content for post ${widget.post.id}',
    );
    AppLogger.debug('FeedItem: hasMedia: ${widget.post.hasMedia}');
    AppLogger.debug('FeedItem: mediaType: ${widget.post.mediaType}');
    AppLogger.debug('FeedItem: isImage: ${widget.post.isImage}');

    if (widget.post.hasMedia) {
      return FutureBuilder<String?>(
        future: widget.post.getMediaUrl(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: GFLoadingIndicator());
          }

          final mediaUrl = snapshot.data;
          if (mediaUrl != null) {
            if (widget.post.isImage) {
              AppLogger.debug('FeedItem: Rendering image with URL: $mediaUrl');
              return GFLazyImage(
                key: ValueKey('image_${widget.post.id}_$mediaUrl'),
                imageUrl: mediaUrl,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.fitWidth,
                onTap: () {
                  AppLogger.debug('FeedItem: Image tapped');
                },
                errorWidget: _buildMediaPlaceholder('Image'),
              );
            } else if (widget.post.isVideo) {
              AppLogger.debug('FeedItem: Rendering video with URL: $mediaUrl');

              // Check if we can use cached video player
              if (_cachedVideoPlayer != null &&
                  _cachedVideoUrl == mediaUrl &&
                  _cachedPostId == widget.post.id) {
                AppLogger.debug('FeedItem: Using cached video player');
                return _cachedVideoPlayer!;
              } else {
                AppLogger.debug(
                  'FeedItem: Creating and caching new video player',
                );
                // Create and cache the video player
                _cachedVideoPlayer = GFVideoPlayer(
                  key: ValueKey('video_${widget.post.id}_$mediaUrl'),
                  videoUrl: mediaUrl,
                  autoPlay: true,
                  showControls: true,
                );
                _cachedVideoUrl = mediaUrl;
                _cachedPostId = widget.post.id;
                return _cachedVideoPlayer!;
              }
            }
          } else {
            AppLogger.debug('FeedItem: No media URL available');
          }

          // Fallback to a gradient background
          return Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
              ),
            ),
          );
        },
      );
    } else {
      AppLogger.debug('FeedItem: No media to display');
    }

    // Fallback to a gradient background
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
    );
  }

  Widget _buildMediaPlaceholder(String type) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == 'Video' ? Icons.play_circle_outline : Icons.image,
              size: 64,
              color: Colors.white70,
            ),
            const SizedBox(height: 16),
            Text(
              '$type Content',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
