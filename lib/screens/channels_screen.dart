import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../components/index.dart';
import '../providers/channels_provider.dart';
import '../models/channel_model.dart';
import '../theme/app_theme.dart';
import 'channel_detail_screen.dart';

class ChannelsScreen extends StatefulWidget {
  const ChannelsScreen({super.key});

  @override
  State<ChannelsScreen> createState() => _ChannelsScreenState();
}

class _ChannelsScreenState extends State<ChannelsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Load channels when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final channelsProvider = Provider.of<ChannelsProvider>(
        context,
        listen: false,
      );
      if (channelsProvider.channels.isEmpty) {
        channelsProvider.loadChannels();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final channelsProvider = Provider.of<ChannelsProvider>(
        context,
        listen: false,
      );
      if (channelsProvider.hasMore && !channelsProvider.isLoading) {
        channelsProvider.loadMoreChannels();
      }
    }
  }

  Future<void> _onRefresh() async {
    final channelsProvider = Provider.of<ChannelsProvider>(
      context,
      listen: false,
    );
    await channelsProvider.refreshChannels();
  }

  void _onChannelTap(ChannelModel channel) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChannelDetailScreen(channel: channel),
      ),
    );
  }

  void _onJoinChannel(ChannelModel channel) {
    // TODO: Implement join channel functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Joined ${channel.name}!'),
        backgroundColor: AppColors.gfGreen,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBlue,
      body: Consumer<ChannelsProvider>(
        builder: (context, channelsProvider, child) {
          if (channelsProvider.status == ChannelsStatus.loading &&
              channelsProvider.channels.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
              ),
            );
          }

          if (channelsProvider.status == ChannelsStatus.error &&
              channelsProvider.channels.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load channels',
                    style: Theme.of(
                      context,
                    ).textTheme.headlineSmall?.copyWith(color: Colors.white),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    channelsProvider.errorMessage ?? 'Unknown error',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => channelsProvider.loadChannels(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.gfGreen,
                      foregroundColor: AppColors.gfDarkBlue,
                    ),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (channelsProvider.channels.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.forum_outlined, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'No channels found',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Check back later for new channels',
                    style: TextStyle(color: Colors.grey, fontSize: 14),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: _onRefresh,
            color: AppColors.gfGreen,
            backgroundColor: AppColors.gfDarkBlue,
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                SliverAppBar(
                  backgroundColor: AppColors.gfDarkBlue,
                  elevation: 0,
                  floating: true,
                  snap: true,
                  toolbarHeight: 0, // Hide the toolbar completely
                  centerTitle: false,
                ),
                SliverPadding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  sliver: SliverGrid(
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 1.0, // Square aspect ratio
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                        ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        if (index >= channelsProvider.channels.length) {
                          return const Center(
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.gfGreen,
                              ),
                            ),
                          );
                        }

                        final channel = channelsProvider.channels[index];
                        return GFChannelCard(
                          channel: channel,
                          onTap: () => _onChannelTap(channel),
                          onJoin: () => _onJoinChannel(channel),
                          showJoinButton: true,
                          isJoined: false, // TODO: Add join status logic
                        );
                      },
                      childCount:
                          channelsProvider.channels.length +
                          (channelsProvider.hasMore &&
                                  channelsProvider.isLoading
                              ? 1
                              : 0),
                    ),
                  ),
                ),
                if (channelsProvider.isRefreshing)
                  const SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.gfGreen,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
